---
title: "modelcontextprotocol/python-sdk: The official Python SDK for Model Context Protocol servers and clients"
source:
  - https://github.com/modelcontextprotocol/python-sdk
published: 
created: 2024-11-26
description: The official Python SDK for Model Context Protocol servers and clients - modelcontextprotocol/python-sdk
tags:
  - AI/ToDO
  - AI/Claude
  - AI/MCP
Reference:
  - https://modelcontextprotocol.io/quickstart
  - https://modelcontextprotocol.io/docs/first-server/python
  - https://github.com/modelcontextprotocol
youtube:
  - https://www.youtube.com/watch?v=63MGCF0feTw
  - https://www.youtube.com/watch?v=KbgDABTSV9I
community:
  - https://github.com/orgs/modelcontextprotocol/discussions
---
# Memo Test
##  🚰 Guide Develop MCP with Cline 
https://docs.cline.bot/mcp-servers/mcp-server-from-scratch
Copy C:\Users\<USER>\Documents\Cline\MCP\.clinerules to your working folder
##  🚰 [[【いま話題】MCPサーバーを10分で構築してみた]]

##  🚰 [[Building an MCP Server]]


## MCP Market
https://glama.ai/mcp/servers
https://cursor.directory/
https://www.lmsystems.ai/servers
https://smithery.ai/
https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer
-- official web
https://github.com/modelcontextprotocol/servers
### BrowserTools
https://browsertools.agentdesk.ai/installation
https://github.com/AgentDeskAI/browser-tools-mcp
git clone https://github.com/AgentDeskAI/browser-tools-mcp.git

- install google extension
  git clone https://github.com/AgentDeskAI/browser-tools-mcp.git
  under extension folder
-  in Cursor
  ~~npx -y @agentdeskai/browser-tools-mcp@1.0.11~~
  ~~/home/<USER>/.nvm/versions/node/v18.20.5/bin/npx -y @agentdeskai/browser-tools-mcp@1.0.11~~
<span style="background:#40a9ff">INPUT BELOW but not npx directly</span>
cmd /c npx @agentdeskai/browser-tools-mcp@1.0.11

Dont use below
  ```
  1. created a symlink from node v18.20.5 to /usr/bin/node
  sudo ln -sf /home/<USER>/.nvm/versions/node/v18.20.5/bin/node /usr/bin/node
2. install package globably
. ~/.nvm/nvm.sh && nvm use 18.20.5 && npm install -g @agentdeskai/browser-tools-mcp@1.0.11
== windows
npm install -g @agentdeskai/browser-tools-mcp@1.0.11

1. run browser-tool-mcp
   
   mcp-proxy --sse-host=0.0.0.0 --sse-port=8512 -- /home/<USER>/.nvm/versions/node/v18.20.5/bin/browser-tools-mcp

mcp-proxy --sse-host=0.0.0.0 --sse-port=8512 -- /home/<USER>/.nvm/versions/node/v18.20.5/bin/browser-tools-mcp

```
  
  
- start server
  npx @agentdeskai/browser-tools-server

### Firecrawl
```
https://github.com/mendableai/firecrawl-mcp-server?tab=readme-ov-file
https://www.firecrawl.dev/

API_KEY
fc-929b7041ace54704a654b408fdd1e43e

```
### magic-mcp 21st.dev
https://github.com/21st-dev/magic-mcp
```
C:\Windows\System32\cmd.exe /c npx -y @smithery/cli@latest run @21st-dev/magic-mcp --config "{\"TWENTY_FIRST_API_KEY\":\"bce0b92155b645041067ddc1c5552559cf753507ad69fae327b3eb150b4267c4\"}"

{
  "mcpServers": {
    "@21st-dev-magic-mcp": {
      "command": "C:\\Windows\\System32\\cmd.exe",
      "args": [
        "/c", "npx","-y",
        "@smithery/cli@latest",
        "run",
        "@21st-dev/magic-mcp",
        "--config",
        "\"{\\\"TWENTY_FIRST_API_KEY\\\":\\\"bce0b92155b645041067ddc1c5552559cf753507ad69fae327b3eb150b4267c4\\\"}\""
      ]
    }
  }
}
```
```
    "magic": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "install",
        "@21st-dev/magic-mcp",
        "--client",
        "claude"
      ],
      "env": {
        "TWENTY_FIRST_API_KEY": "bce0b92155b645041067ddc1c5552559cf753507ad69fae327b3eb150b4267c4"
      }
    },    
```
### Replicate
npm install -g mcp-replicate
```
    "replicate": {
      "command": "mcp-replicate",
      "env": {
        "REPLICATE_API_TOKEN": "****************************************"
      }
    },	 
```

### obsidian MCP
https://github.com/StevenStavrakis/obsidian-mcp
https://github.com/jacksteamdev/obsidian-mcp-tools
https://github.com/coddingtonbear/obsidian-local-rest-api
```
    "obsidian-mcp-tools": {
      "command": "env OBSIDIAN_API_KEY=\"447fc86b1ac3cbc5bd9f2f7429e6e9ce72d12bc46625df8377cadcb0666a6e71\"; 'D:\\My Drive\\AIResearch\\Obsidian\\AI\\.obsidian\\plugins\\mcp-tools\\bin\\mcp-server.exe'"
    }
  }
```
### Figma MCP
https://github.com/JayZeeDesign/figma-mcp
https://www.figma.com/
-- start manually
uv run figma-mcp --figma-api-key=*********************************************
-- start from cursor
c:\Users\<USER>\.local\bin\uv --directory G:\ResearchDirection\AI\figma-mcp run figma-mcp --figma-api-key=*********************************************
-- start from claude 
```
    "figma-python": {
      "command": "uv",
      "args": [
        "--directory",
        "G:\\ResearchDirection\\AI\\figma-mcp",
        "run",
        "figma-mcp",	  
        "--figma-api-key=*********************************************"
      ]
    },	  

```
### youtube download
https://glama.ai/mcp/servers/0nvr1xbmpk
winget install yt-dlp
and add below to claude desktop
```
    "yt-dlp": {
      "command": "npx",
      "args": [
        "-y",
        "@kevinwatt/yt-dlp-mcp"
      ]
    }
```

### markdown2pdf
```
# Clone the repository
git clone https://github.com/2b3pro/markdown2pdf-mcp.git

# Navigate to the project directory
cd markdown2pdf-mcp

# Install dependencies
npm install

# Build the project
npm run build
```


sear### search_server markdown_2_pdf 
dependency installation
```
# uv run python -m src.search_server.server --port 8512 --transport sse
uv run search-server --transport sse --port 8511
```
#### install pandoc to windows
```
## install dependency
https://github.com/jgm/pandoc/releases/tag/2.2.1
#Dont use http://www.texts.io/download/
https://miktex.org/download
https://wkhtmltopdf.org/downloads.html

Then on miktex update pacakges
```
#### install package to jethome64
```
sudo apt-get install poppler-utils ttf-mscorefonts-installer msttcorefonts fonts-crosextra-caladea fonts-crosextra-carlito gsfonts lcdf-typetools
sudo apt-get install pandoc texlive-xetex
sudo apt-get install texlive-xetex texlive-fonts-recommended texlive-lang-cjk
sudo apt-get install texlive-xetex texlive-lang-japanese fonts-noto-cjk fonts-ipafont
sudo apt-get install texlive-lang-japanese fonts-ipafont fonts-ipaexfont
sudo apt-get install fonts-noto-cjk fonts-ipafont fonts-vlgothic

```
#### dart-mcp server
https://github.com/its-dart/dart-mcp-server.git
```
{
  "mcpServers": {
    "dart": {
      "command": "npx",
      "args": ["-y", "dart-mcp-server"],
      "env": {
        "DART_TOKEN": "dsa_..."
      }
    }
  }
}
```
npx -y supergateway --port 8516 --stdio "DART_TOKEN=dsa_6078e6e85fe9e37fdd88fece2b3f1a50993714996a0ca859758d07a775bb7da5 npx -y dart-mcp-server"


#### Manually convert markdown file to pdf
G:\ResearchDirection\AI\search_server\convert_japanese_files.py
D:\My Drive\AIResearch\Obsidian\AI\Notion\markdown_to_pdf.py

### huggingface mcp server
https://github.com/shreyaskarnik/huggingface-mcp-server

npx -y supergateway --port 8517 --stdio "uv --directory /opt/app/cline/huggingface-mcp-server run huggingface"
```
    "huggingface": {
      "command": "uv",
      "args": [
        "--directory",
        "G:\\ResearchDirection\\AI\\huggingface-mcp-server",
        "run",
        "huggingface"
      ],
      "timeout": 60000
    },	
```

```
git clone  https://github.com/shreyaskarnik/huggingface-mcp-server.git
uv sync --python "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"

```

### Spotify mcp
https://github.com/varunneal/spotify-mcp?tab=readme-ov-file

https://developer.spotify.com/
```
	"spotify": {
		"command": "uv",
		"args": [
		"--directory",
		"G:\\ResearchDirection\\AI\\spotify-mcp",
		"run",
		"spotify-mcp"
		],
		"env": {
		"SPOTIFY_CLIENT_ID": "23467d78d46b4b5688183b7fbb9ad29a",
		"SPOTIFY_CLIENT_SECRET": "5afac8173c1d48e4816aedcb137d7dda",
		"SPOTIFY_REDIRECT_URI": "http://localhost:8889"
		}
	},	  
```

### Supabase mcp
https://github.com/supabase-community/supabase-mcp?tab=readme-ov-file#windows
https://supabase.com/docs/guides/getting-started/mcp

<span style="background:#40a9ff">working</span>
step1 git clone or git pull
step2 npm install
step 3 npm run build
      npm run build --workspace @supabase/mcp-server-postgrest
            
step 4 setup claude
```
    "supabase": {
      "command": "node",
      "args": [
        "G:\\ResearchDirection\\AI\\supabase-mcp\\packages\\mcp-server-supabase\\dist\\stdio.js",
        "--access-token",
        "********************************************"
      ]
    },	
```

```
# not working
    "supabase": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ]
    }

# work in below
    "supabase": {
      "command": "wsl",
      "args": [
        "npx",
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ]
    }

    "supabase": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ]
    }
```
npx -y supergateway --port 8518 --stdio "npx -y @supabase/mcp-server-supabase@latest --access-token ********************************************"

npx -y @supabase/mcp-server-supabase@latest --access-token=********************************************


### Jupyter MCP server
https://github.com/ihrpr/mcp-server-jupyter?tab=readme-ov-file

#### installation
```
uv venv --seed
.venv\Scripts\activate
uv add jupyterlab

uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
uv add torch
uv add torchvision

AssertionError: Torch not compiled with CUDA enabled
```
#### start/setup
```
-- start jupyter lab
runit
.venv\Scripts\jupyter.exe lab --notebook-dir=G:\ResearchDirection\AI\mcp-server-jupyter
.venv/bin/jupyter lab <<= run in unix

-- setup in claude
    "Jupyter-notebook-manager": {
      "command": "uv",
      "args": [
        "run",
        "--directory",
        "G:\\ResearchDirection\\AI\\mcp-server-jupyter\\src\\mcp_server_jupyter",
        "mcp-server-jupyter"
      ],
      "env": {
        "UV_PROJECT_ENVIRONMENT": "G:\\ResearchDirection\\AI\\mcp-server-jupyter\\.venv"
      }
    },  

-- install kernel <-- looks useless and the library decided by UV_PROJECT_ENVIRONMENT
conda create -n xxx python=3.12
conda activate xxx
pip install torch torchvision xxx
python -m ipykernel install --user --name mom --display-name "MCP Environment"
```
#### communicate with your notebook
Must add use absolute path of your notebook
```
use G:\ResearchDirection\AI\mcp-server-jupyter\MCPServer.ipynb
pls get past month BTC price and plot the graph

use G:\ResearchDirection\AI\mcp-server-jupyter\MCPServer.ipynb
let's check gpu count and gpu details with pytorch

```
### Sequential Thinking MCP Server
```
git clone https://github.com/zengwenliang416/mcp-server-sequential-thinking.git
cd mcp-server-sequential-thinking
npm install
npm run build

    "sequential-thinking": {
      "command": "node",
      "args": [
        "G:\\ResearchDirection\\AI\\mcp-server-sequential-thinking\\dist\\index.js"
      ]
    },	

npx -y supergateway@latest --port 8520 --stdio "node /opt/app/cline/mcp-server-sequential-thinking/dist/index.js"
```
### mcp memory service
git clone https://github.com/doobidoo/mcp-memory-service.git
cd mcp-memory-service
uv venv --seed
.venv/bin/activate
```
   "memory": {
     "command": "uv",
     "args": [
       "--directory",
       "G:\\ResearchDirection\\AI\\mcp-memory-service",
       "run",
       "memory"
     ],
     "env": {
       "MCP_MEMORY_CHROMA_PATH": "G:\\ResearchDirection\\AI\\mcp-memory-service\\chroma_db",
       "MCP_MEMORY_BACKUPS_PATH": "G:\\ResearchDirection\\AI\\mcp-memory-service\\backup"
     }
    },	
```
### context7 get latest api
https://github.com/upstash/context7

```
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }

npx -y @upstash/context7-mcp@latest
npx -y supergateway@latest --port 8519 --stdio "npx -y @upstash/context7-mcp@latest"
```
### Interactive Feedback MCP 
mcp-feedback-enhanced
https://github.com/poliva/interactive-feedback-mcp
https://www.cnblogs.com/lf109/p/18906090
git clone https://github.com/poliva/interactive-feedback-mcp.git
-- currently only can run in windows
uv sync
```
    "mcp-feedback-enhanced": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ]
    }
    
    "mcp-feedback-enhanced": {
          "command": "uv",
          "args": [
            "--directory",
            "G:\\ResearchDirection\\AI\\interactive-feedback-mcp",
            "run",
            "server.py"
          ],
          "timeout": 60000
    }	
        
-- cursor
    "mcp_feedback_enhanced": {
      "command": "C:/Users/<USER>/.local/bin/uv.exe",
      "args": [
        "--directory",
        "C:/Work/Research/AI/mcp_feedback_enhanced",
        "run",
        "server.py"
      ],
      "timeout": 60000
    }
  
```
rule/prompt
```
If requirements or instructions are unclear use the tool mcp-feedback-enhanced to ask clarifying questions to the user before proceeding, do not make assumptions. Whenever possible, present the user with predefined options through the mcp-feedback-enhanced MCP tool to facilitate quick decisions.

Whenever you're about to complete a user request, call the mcp-feedback-enhanced tool to request user feedback before ending the process. If the feedback is empty you can end the request and don't call the tool in loop.
```

### manim mcp tool
https://github.com/abhiemj/manim-mcp-server

```
sudo apt install python3-dev
sudo apt install libcairo2-dev pkg-config
sudo apt install libglib2.0-dev libpixman-1-dev libfreetype6-dev libpng-dev

-- to avoid head file not find error
find /usr/include -name "Python.h"
sudo mkdir -p /install/include/python3.10
cd /install/include
sudo ln -s /usr/include/python3.10 python3.10

uv add manim
```

### OpenProject MCP
https://www.openproject.org/docs/api/example/
https://github.com/jessebautista/mcp-openproject-smithery

-- input information in .env
cp -rp .env.example .env
```
OPENPROJECT_API_KEY="f35b86fd07dfbc5561379a024e0764521fd81f0e9bdc23e5db2e7295dc7a3b47"
OPENPROJECT_URL="https://pmt.sbigenai.com"
OPENPROJECT_API_VERSION="v3"
PORT = 8523
MCP_TRANSPORT_MODE="sse"

```
npm install
npm build
npm run dev
npm start

As /mcp mode not support well in claude desktop and cursor both , i added /sse mode
push to my repo
https://github.com/netcaster1/openproject_mcp.git

    "openproject": {
      "url": "http://192.168.0.18:8523/sse"
    },

### Zen MCP
```
# Clone to your preferred location
git clone https://github.com/BeehiveInnovations/zen-mcp-server.git
cd zen-mcp-server

# One-command setup (includes Redis for AI conversations)
./run-server.sh

above command will include rebuild image(you need to docker image rm id, manually remove old)
Next time we can just run docker compose up -d


# Add the MCP server directly via Claude Code CLI
claude mcp add zen -s user -- docker exec -i zen-mcp-server python server.py

# List your MCP servers to verify
claude mcp list

# mcp config
  "mcpServers": {
    "zen": {
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "--env-file", "/path/to/zen-mcp-server/.env",
        "-e", "WORKSPACE_ROOT=/Users/<USER>",
        "-v", "/Users/<USER>/workspace:ro",
        "zen-mcp-server:latest"
      ]
    }
    
  "zen": {
      "command": "docker",
      "args": [
        "exec",
        "-i",
        "zen-mcp-server",
        "python",
        "server.py"
      ]
  }
- wsl
    "zen": {
      "command": "wsl.exe",
      "args": [
        "docker",
        "exec",
        "-i",
        "zen-mcp-server",
        "python",
        "server.py"
      ]
    }
    "zen": {
      "command": "wsl.exe",
      "args": [
        "/opt/app/mcp/zen-mcp-server/.zen_venv/bin/python",
        "/opt/app/mcp/zen-mcp-server/server.py"
      ]
    },
    
    
    
--- new verion sth changed
! Found old Docker-based Zen registration, updating...
Removed MCP server zen from user config
Added stdio MCP server zen with command: /opt/app/mcp/zen-mcp-server/.zen_venv/bin/python /opt/app/mcp/zen-mcp-server/server.py to user config
✓ Updated Zen to become a standalone script
./run-server.sh: line 128: win_appdata: unbound variable
! Unable to determine Claude Desktop config path for this platform

Logs will be written to: /opt/app/mcp/zen-mcp-server/logs/mcp_server.log

To follow logs: ./run-server.sh -f
To show config: ./run-server.sh -c
To update: git pull, then run ./run-server.sh again
```
### Line Integration
```

# Line MCP Server Setup
https://github.com/line/line-bot-mcp-server

## Line website Setup 
### 注册LINE Business ID
https://developers.line.biz/en/docs/messaging-api/getting-started/#create-oa
Then you will get your business account site
https://manager.line.biz/account/@343eqccf/


### Enable Message API
https://developers.line.biz/en/docs/messaging-api/getting-started/#using-oa-manager
 Settings → Messaging API
Channel ID: **********
Channel Secret: 3b3b242644794066aacec6a46dcb625c
USer ID: U703acf75e34221cab1760fef9e92ba74
 
### Confirm If Channel/Provider has been created
https://developers.line.biz/console/

### 获取Channel Access Token
https://developers.line.biz/console/
Claude Code Provider -> Messaging API -> Issue Token
x4wlJpWAgQ8jLqKFqWPflxCXksJL29kTWppgTC2C/U0D59WoCjX0EYIZBQyP4rJnEn/PQQvW7yMNfErQosnqWc4OmhtyYOBhrtKD4m/90TP1VZMgE3I/MoCwWPXayGyQqy+Ve4qxadqR71hmnEUHGQdB04t89/1O/w1cDnyilFU=
Claude Code Provider -> Basic Settings -> User ID
U703acf75e34221cab1760fef9e92ba74

## Claude Code side setup

npx @line/line-bot-mcp-server
-- glabal mcp setup
vi ~/.claude.json
    "line-notifications": {
      "command": "npx",
      "args": ["@line/line-bot-mcp-server"],
      "env": {
        "CHANNEL_ACCESS_TOKEN": "x4wlJpWAgQ8jLqKFqWPflxCXksJL29kTWppgTC2C/U0D59WoCjX0EYIZBQyP4rJnEn/PQQvW7yMNfErQosnqWc4OmhtyYOBhrtKD4m/90TP1VZMgE3I/MoCwWPXayGyQqy+Ve4qxadqR71hmnEUHGQdB04t89/1O/w1cDnyilFU=",
        "DESTINATION_USER_ID": "U703acf75e34221cab1760fef9e92ba74"
      }
    }
-- /memory -user level rules in ~/.claude/CLAUDE.md
Rules:
1. After task done, Pls send detail current task stage related information and task status to line with line-notification tool
2. before you want to get user feedback, pls send detail current task stage related information and task status to line with line-notifcation tool

-- send one test message and apply permission and no need user approval


```
#### Add chat support (Optional)
https://claude.ai/public/artifacts/c62e62a4-c52c-4624-bf30-96546153d054
jethome64
/opt/app/cline/claude-line-bot
start ngrok and update webhook
https://developers.line.biz/console/channel/**********/messaging-api


### playwright
```
claude mcp add playwright npx '@playwright/mcp@latest'
```


---

## Debug
npx @modelcontextprotocol/inspector npx -y @supabase/mcp-server-supabase@latest --access-token ********************************************
Then while that runs, go to http://127.0.0.1:6274/ and hit "Connect".

---
---
## UV enviroment management

### Command explanation 
Simple explanation
https://www.youtube.com/watch?v=aVXs8lb7i9U
#### <span style="background:#40a9ff">Check python version which </span>
 ```
 uv python list
 uv python install cpython-3.12
 uv run -p 3.12 ai.py
```
#### [[Kaggle ARC Prize 2024]]
#### <span style="background:#40a9ff">"uv RUN pip install" vs 'uv pip install' vs 'uv add'</span>
uv run pip install will update system default enviroment
uv pip install will update .venv environment<span style="background:#b1ffff"> <font color="#ff0000">but wont update pyproject.toml 这将导致你运行uv run如果不一致会同pyproject文件里的库版本对齐</font></span>
uv add will update venv environment and also update pyproject.toml

#### <span style="background:#40a9ff">'uv run xxx.py' vs 'python xxx.py after activate venv'</span>
uv run sss.py -> it will check pyproject.toml and sync pacakges version <font color="#ff0000">(reinstall if not same as pyproject file)</font>
python xxx.py will use current venv packages to run this python file 

#### <span style="background:#40a9ff">uv tool - Installing Global Tools  安装全局工具</span>
uv tool install black
uv tool list

#### <span style="background:#40a9ff">使用 `uvx` 零时运行工具</span>
uvx black my_script.py

#### <span style="background:#40a9ff">打印依赖树</span>
uv tree

#### 作为dev工具安装
uv add xxx --dev
#### 移除package
uv remove xxxx

#### Upgrade package
uv add -U xxxx

#### <span style="background:#40a9ff">创建虚拟环境</span>
uv init -p 3.12 hello
cd hello

Or
cd hello
uv init -p 3.12

uv sync # it will genereate venv and install python 3.12
source .venv/bin/activate
uv add xxx # it will install packages into .venv
deactivate # quite virtual environment
```
uv add /opt/install/jetpack6/v61/offcial/torch-2.5.0-cp310-cp310-linux_aarch64.whl /opt/install/jetpack6/v61/offcial/torchvision-0.20.0-cp310-cp310-linux_aarch64.whl /opt/install/jetpack6/v61/offcial/torchaudio-2.5.0-cp310-cp310-linux_aarch64.whl /opt/install/jetpack6/v61/offcial/opencv_python-4.10.0-py3-none-any.whl /opt/install/jetpack6/v61/offcial/opencv_contrib_python-4.10.0+6b45caa-cp310-cp310-linux_aarch64.whl /opt/install/jetpack6/v61/offcial/triton-3.1.0-cp310-cp310-linux_aarch64.whl  


-- new version 2.7
uv add /databank/install/jetpack6/v61/offcial/torch-2.7.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchvision-0.22.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchaudio-2.7.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_python-4.11.0-py3-none-any.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_contrib_python-*********-cp310-cp310-linux_aarch64.whl

-- new version 2.8
uv add /databank/install/jetpack6/v61/offcial/torch-2.8.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchvision-0.23.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchaudio-2.8.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_python-4.11.0-py3-none-any.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_contrib_python-*********-cp310-cp310-linux_aarch64.whl

```
<span style="background:#fff88f">DONT USE uv run pip install as it will install pcakage into global</span>
display package installed in .venv
uv pip list
<span style="background:#40a9ff">#### reinstall venv</span>
uv venc # it will recreate new .venv folder and all previous packages are gone
uv venv --seed
source .venv/bin/activate

uv sync # it will reinstall all packages. based on pyproject.toml file
Running<span style="background:#40a9ff"> uv sync --reinstall </span>
#### <span style="background:#40a9ff">打包</span>
edit pyproject.toml
[project.scripts]
search-server = "search_server:main"
uv build

```

uv add pyinstaller
# generate the initial .spec file. We'll target src/main.py as the entry point and use the --windowed option to hide the console window by default.
# Add Icon: Specify an icon file (e.g., resources/icons/app_icon.ico) for the executable using the icon argument in the EXE section. Let's assume an icon exists at this path.
uv run pyinstaller --name AutoTrans --windowed --noconfirm src/main.py
uv add pyinstaller-versionfile
uv run pyinstaller AutoTrans.spec
uv run pyinstaller AutoTrans.spec --clean

# INFO: Build complete! The results are available in: G:\ResearchDirection\AI\AutoTrans\dist

```

#### Auto install right cuda-enabled pytorch
```
You can set `UV_TORCH_BACKEND=auto` and uv will automatically install the right CUDA-enabled PyTorch for your machine, zero configuration

export UV_TORCH_BACKEND=auto
uv pip install torch

-- uv add torch still not supported
```

#### nest uv environment
[[UV dependency-management-options]]

----
## installation
https://medium.com/@robert-mcdermott/saying-goodbye-to-anaconda-91c18ddf89bb
```
curl -LsSf https://astral.sh/uv/install.sh | sh
uv python install <version>
-- uv python install 3.10
uv init -p 3.12 dad-jokes
cd dad-jokes
uv add requests
uv pip compile pyproject.toml -o requirements.txt
uv run python
uv run dad-jokes.py

```

-- Reinstall uv venv environment
```
rm -rf .venv
uv python install 3.10
uv venv
source .venv/bin/activate
uv pip install .
or
uv pip install -r requirements.txt

```
If in uv to install torchvision
```
(arc-prize-2024) [23:58:08]ray@jethome64:/opt/app/arc-prize-2024/v61/vision-0.20.0$ uv run python setup.py install
error: No `project` table found in: `/databank/install/jetpack6/v61/vision-0.20.0/pyproject.toml`
(arc-prize-2024) [23:58:23]ray@jethome64:/opt/app/arc-prize-2024/v61/vision-0.20.0$ uv run pip install .
error: No `project` table found in: `/databank/install/jetpack6/v61/vision-0.20.0/pyproject.toml`
(arc-prize-2024) [23:58:44]ray@jethome64:/opt/app/arc-prize-2024/v61/vision-0.20.0$ uv pip install -e .
Using Python 3.10.16 environment at: /databank/app/arc-prize-2024/.venv

we use uv pip install -e .

```
another way
```
mv pyproject.toml pyproject.toml_
uv run python setup.py install
```

-- Install MCP
```
1. install uv
   https://docs.astral.sh/uv/#__tabbed_1_2
   
   powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
   
   To add C:\Users\<USER>\.local\bin to your PATH, either restart your system or run:
   
       set Path=C:\Users\<USER>\.local\bin;%Path%   (cmd)
       $env:Path = "C:\Users\<USER>\.local\bin;$env:Path"   (powershell)
   
2. install mcp

uv add mcp
Or
conda activate mlearn
pip install mcp


3. 
uvx create-mcp-server --path weather_service
cd weather_service
uv add httpx python-dotenv

```

## code
D:\My Drive\AIResearch\codespace\search_server

Copy code to new place and install with below steps
```
conda create -n mcp python=3.10
cd G:\ResearchDirection\AI\search_server
uv add mcp
#pip install mcp
#pip install -r requirements.txt
uv add --dev pyright
uv run pyright src
uv add --dev pytest
uv run pytest -v
update pyproject.toml 
====
[project]
name = "search_server"
version = "1.0.0"
description = "MCP Search Server"
authors = [
    { name = "Ray Sheng", email = "<EMAIL>" }
]
dependencies = [
    "requests>=2.32.3",
    "pydantic>=2.0.0",
    "mcp>=1.0.0",
]
requires-python = ">=3.10"

[project.scripts]
search-server = "search_server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "pyright>=1.1.389",
    "pytest>=8.3.3",
]
====

uv pip install -e .
uv run search-server
G:\ResearchDirection\AI\search_server\notepad\claude_desktop_config.json
====
{
    "mcpServers": {
      "search_server": {
        "command": "uv",
        "args": [
          "--directory",
          "C:\\Work\\Research\\AI\\search_server",
          "run",
          "search-server"
        ],
        "env": {
          "TAVILY_API_KEY": "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",
          "SERPER_API_KEY": "e8d813d64fd9c79f007c9c0e74c98d8d5ac1d559",
          "BING_API_KEY": "********************************",
          "GOOGLE_API_KEY": "AIzaSyB1RJwM8-r_ozVBhnqRz7LXYmtA_QyE5eo",
          "GOOGLE_SEARCH_ENGINE_ID": "5531449e036a443c3"
        }
      }
    }
  }
====

```
## check log
```
Then we enable claude desktop developer mode and restart claude desktop. 

C:\Users\<USER>\AppData\Roaming\Claude\logs\mcp-server-search_server.log
C:\Users\<USER>\AppData\Roaming\Claude\logs\mcp.log

------------------------------------------
```

## Run code in e2b.dev
https://github.com/e2b-dev/mcp-server.git

### create a custom sandbox
docker desktop -> Settings -> Docker Engine
![[Pasted image 20241202183509.png]]
```
G:\ResearchDirection\AI\mcp-server\js\src\index.ts
https://e2b.dev/docs/quickstart/install-custom-packages
https://e2b.dev/docs/sandbox-template

npm i -g @e2b/cli
e2b auth login
--create e2b.Dockerfile
e2b template init
-- Edit the E2B Dockerfile to install the packages you need.
# You can use most Debian-based base images
FROM e2bdev/code-interpreter:latest

# Install dependencies and customize sandbox
RUN pip install yfinance pandas

# Enable insecure registries in local docker engine
unexpected status from HEAD request to https://docker.e2b.dev/v2/e2b/custom-envs/vtr3n8sgncl3dumyvo1q/blobs/sha256:4063cffa4289f61d15eed9e8927c08b20d0adf22376673958267ffa290658ac6: 401 Unauthorized

https://e2b.dev/docs/troubleshooting/templates/build-authentication-error

{
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  },
  "features": {
    "buildkit": true
  },
  "experimental": false,
  "insecure-registries": [
    "host.docker.internal:49984"
  ]
}

-- build 
e2b template build -c "/root/.jupyter/start-up.sh"
✅ Building sandbox template vtr3n8sgncl3dumyvo1q finished.

-- modify mcp code

  private async setupSandbox(): Promise<void> {
    this.sandbox = await Sandbox.create(
      "vtr3n8sgncl3dumyvo1q",
      {
      apiKey: API_KEY,
      timeoutMs: 300000, // 5 minutes timeout
      }
    );
  }

-- build mcp
npm install

Build the server:

npm run build



--- some other command
e2b sandbox list
e2b sandbox kill <sandbox-id>
e2b sandbox kill --all



```
## SSE Gateway
### supergateway
Run MCP Stdio servers over SSE <= refer to 

### mcp server gateway
https://github.com/boilingdata/mcp-server-and-gw.git
G:\ResearchDirection\AI\mcp-server-and-gw

### mcp-proxy
https://github.com/sparfenyuk/mcp-proxy
```python
https://github.com/sparfenyuk/mcp-proxy
- install
uv tool install git+https://github.com/sparfenyuk/mcp-proxy
uv tool uninstall mcp-proxy
- run
# Start the MCP server behind the proxy
mcp-proxy uvx mcp-server-fetch

# Start the MCP server behind the proxy with a custom port
mcp-proxy --sse-port=8080 uvx mcp-server-fetch

# Start the MCP server behind the proxy with a custom host and port
mcp-proxy --sse-host=0.0.0.0 --sse-port=8080 uvx mcp-server-fetch

# Start the MCP server behind the proxy with a custom user agent
# Note that the `--` separator is used to separate the `mcp-proxy` arguments from the `mcp-server-fetch` arguments
mcp-proxy --sse-port=8080 -- uvx mcp-server-fetch --user-agent=YourUserAgent


mcp-proxy --sse-host=0.0.0.0 --sse-port=8511 -- /home/
ray/.local/bin/uv --directory /opt/app/cline/search_server run search-server --transport stdio

mcp-proxy --sse-host=0.0.0.0 --sse-port=8512 -- /home/
ray/.local/bin/uv --directory /opt/app/cline/search_server run search-server --transport stdio

- Setup
{
  "mcpServers": {
    "mcp-proxy": {
        "command": "mcp-proxy",
        "args": ["http://example.io/sse"],
        "env": {
          "API_ACCESS_TOKEN": "access-token"
        }
    }
  }
}
e

```
## some official packages
https://x.com/donvito/status/1863096309949022599
https://github.com/punkpeye/awesome-mcp-servers
https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer
```
https://ban.com/blog/claude-mcp-puppeteer-%E3%82%92%E6%93%8D%E4%BD%9C%E3%81%99%E3%82%8B/
install manaully at global
npm install -g @modelcontextprotocol/server-puppeteer

C:\Users\<USER>\AppData\Roaming\nvm\v23.1.0\node_modules\@modelcontextprotocol\server-puppeteer
    "puppeteer": {
      "command": "node",
      "args": [
	  "C:\\Users\\<USER>\\AppData\\Roaming\\nvm\\v23.1.0\\node_modules\\@modelcontextprotocol\\server-puppeteer\\dist\\index.js"
	  ]
    }	


```
https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem
```
npm install -g @modelcontextprotocol/server-filesystem

    "filesystem": {
      "command": "node",
      "args": [
	  "C:\\Users\\<USER>\\AppData\\Roaming\\nvm\\v23.1.0\\node_modules\\@modelcontextprotocol\\server-filesystem\\dist\\index.js",
	  "F:\\OneDrive\\Desktop",
	  "D:\\My Drive\\Download"
	  ]
    }	
```
## Debug MCP
https://youtu.be/4oJ8Sx9BOhk

Client side
1. Enable DevTools
   notepad "C:\Users\<USER>\AppData\Roaming\Claude\developer_settings.json"
```
{
     "allowDevTools": true
}   
```
      
1.  ctl+shift+i
  Not working
  
Server side
```
npx @modelcontextprotocol/inspector uv --directory G:\ResearchDirection\AI\search_server run search-server --transport stdio
```
```
https://lewoudar.medium.com/anyio-all-you-need-for-async-programming-stuff-4cd084d0f6bd
https://anyio.readthedocs.io/en/stable/api.html#anyio.run

# ExceptionGroup issue
exceptiongroup.ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
uv add exceptiongroup

def value_type_error_handler(exc_group: ExceptionGroup) -> None:
    for exc in exc_group.exceptions:
        print(exc)	

async def arun():
    with catch({
        (ValueError, TypeError): value_type_error_handler,
    }):
        async with mcp.server.stdio.stdio_server() as streams:
            await server.run(
                streams[0],
                streams[1],
                InitializationOptions(
                    server_name="search_server",
                    server_version="1.3.3",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )
    # await asyncio.sleep(1)

logger.info("Starting stdio server")
# anyio.run(arun, backend="trio")
anyio.run(arun)		
```
## Tavily MCP

https://github.com/tavily-ai/tavily-mcp


## Run MCP Stdio servers over SSE
https://github.com/supercorp-ai/supergateway

```
git clone https://github.com/supercorp-ai/supergateway
cd supergateway
npm install
npm run build 

npx -y supergateway --port 8511 --stdio "uv --directory /opt/app/cline/search_server run search-server --transport stdio"

# In another terminal:
ngrok http 8000
```
![[Pasted image 20250202151259.png]]

Setup in Cursor
search_server  sse
http://192.168.0.18:8511/sse

## MCP used by Cursor

```
/home/<USER>/.local/bin/uv --directory /opt/app/cline/search_server run search-server --transport stdio

```
```
env TAVILY_API_KEY=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 /home/<USER>/.nvm/versions/node/v22.11.0/bin/npx -y /opt/app/cline/tavily-mcp/build/index.js

```

```
http://192.168.0.18:8511/sse
```
## MCP used by Cline
```
{
  "mcpServers": {
    "search_server": {
      "command": "uv",
      "args": [
        "--directory",
        "/opt/app/cline/search_server",
        "run",
        "search-server",
        "--transport",
        "stdio"
      ],
      "env": {
        "TAVILY_API_KEY": "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",
        "SERPER_API_KEY": "e8d813d64fd9c79f007c9c0e74c98d8d5ac1d559",
        "BING_API_KEY": "********************************",
        "GOOGLE_API_KEY": "AIzaSyB1RJwM8-r_ozVBhnqRz7LXYmtA_QyE5eo",
        "GOOGLE_SEARCH_ENGINE_ID": "5531449e036a443c3",
        "JINA_API_KEY": "jina_2b11a07ea56447e685805d405dec3655CVww68NtHgi1_Mnmf3K-Dis9VqlY",
        "KNOWLEDGE_BASE_URL": "http://192.168.0.16:3201",
        "LINKUP_API_KEY": "ea1ae2fc-4b58-435a-95ca-c2f51af2cda5",
        "EXA_API_KEY": "8b60983b-714d-4815-8748-53b1606929ca"
      },
      "autoApprove": [
        "search",
        "scrape-url"
      ],
      "disabled": false
    }
  }
}
```

# MCP Client Development

[[🦜🦜🦜MCP_Client_Development_Research]]
```
/opt/workspace/app/cursor/jina/mcp/mcp_example.py

/databank/workspace/miniconda3/envs/mlearn/lib/python3.10/site-packages/mcp/client
```

---

# MCP Integrations Development


https://support.anthropic.com/en/articles/********-about-custom-integrations-using-remote-mcp
https://ngrok.com/docs/traffic-policy/actions/oauth/
https://ngrok.com/docs/integrations/google/oauth/
https://developers.google.com/identity/protocols/oauth2/web-server#python



## What to learn 
 request.scope["auth_result"] = {"success": True} 
```
Consolidated Middleware: I introduced a new middleware called ClaudeAuthIntegrationMiddleware. This middleware is designed to run early in the request lifecycle. It specifically checks for incoming requests with a Bearer token from the Claude client ID (********-ff92-4c28-b15f-32186412aacd). If a valid token is found, it verifies it using the OAuth provider and then correctly injects the necessary authentication information (user ID, scopes, authenticated status) into the request.scope. Crucially, it sets request.scope["auth_result"] = {"success": True} which signals to FastMCP's internal authentication mechanism that the request is already authenticated, allowing it to proceed through the standard pathways.

```
Flow
https://claude.ai/public/artifacts/53fc346c-afae-4e1a-bdf7-3c9685e660be

---
# MCP Python SDK

[![PyPI](https://camo.githubusercontent.com/e6ba71e25e692956bce8d9b0b4e043d9b7171186941670af455088139928be55/68747470733a2f2f696d672e736869656c64732e696f2f707970692f762f6d63702e737667)](https://pypi.org/project/mcp/) [![MIT licensed](https://camo.githubusercontent.com/98147347f1be2b00361083e2aac1a18781acb3109ca688b1cd1940980e9f1201/68747470733a2f2f696d672e736869656c64732e696f2f707970692f6c2f6d63702e737667)](https://github.com/modelcontextprotocol/python-sdk/blob/main/LICENSE) [![Python Version](https://camo.githubusercontent.com/b33b4fb36a9335985026e9b5b20cf5b1e548b7fff9f215b25abd31c9eaaa04ff/68747470733a2f2f696d672e736869656c64732e696f2f707970692f707976657273696f6e732f6d63702e737667)](https://www.python.org/downloads/) [![Documentation](https://camo.githubusercontent.com/301bdc40b0f2893b417e920988f8aac322e3adab80c8a6c32657286f4aaf3a48/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f646f63732d6d6f64656c636f6e7465787470726f746f636f6c2e696f2d626c75652e737667)](https://modelcontextprotocol.io/) [![Specification](https://camo.githubusercontent.com/0e20327998ce56e7a24c9b61227bb10976c5c3b6188551c2bd37e357ad67e7da/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f737065632d737065632e6d6f64656c636f6e7465787470726f746f636f6c2e696f2d626c75652e737667)](https://spec.modelcontextprotocol.io/) [![GitHub Discussions](https://camo.githubusercontent.com/587d3a9857dcc52c6f99b5109e13afc68542ab73eb8160f6a36722bd83a2cb1b/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f64697363757373696f6e732f6d6f64656c636f6e7465787470726f746f636f6c2f707974686f6e2d73646b)](https://github.com/modelcontextprotocol/python-sdk/discussions)

Python implementation of the [Model Context Protocol](https://modelcontextprotocol.io/) (MCP), providing both client and server capabilities for integrating with LLM surfaces.

## Overview

The Model Context Protocol allows applications to provide context for LLMs in a standardized way, separating the concerns of providing context from the actual LLM interaction. This Python SDK implements the full MCP specification, making it easy to:

- Build MCP clients that can connect to any MCP server
- Create MCP servers that expose resources, prompts and tools
- Use standard transports like stdio and SSE
- Handle all MCP protocol messages and lifecycle events

## Installation

We recommend the use of [uv](https://docs.astral.sh/uv/) to manage your Python projects:

Alternatively, add mcp to your `requirements.txt`:

```
pip install mcp
# or add to requirements.txt
pip install -r requirements.txt
```

## Overview

MCP servers provide focused functionality like resources, tools, prompts, and other capabilities that can be reused across many client applications. These servers are designed to be easy to build, highly composable, and modular.

### Key design principles

- Servers are extremely easy to build with clear, simple interfaces
- Multiple servers can be composed seamlessly through a shared protocol
- Each server operates in isolation and cannot access conversation context
- Features can be added progressively through capability negotiation

### Server provided primitives

- [Prompts](https://modelcontextprotocol.io/docs/concepts/prompts): Templatable text
- [Resources](https://modelcontextprotocol.io/docs/concepts/resources): File-like attachments
- [Tools](https://modelcontextprotocol.io/docs/concepts/tools): Functions that models can call
- Utilities:
- Completion: Auto-completion provider for prompt arguments or resource URI templates
- Logging: Logging to the client
- Pagination\*: Pagination for long results

### Client provided primitives

- [Sampling](https://modelcontextprotocol.io/docs/concepts/sampling): Allow servers to sample using client models
- Roots: Information about locations to operate on (e.g., directories)

Connections between clients and servers are established through transports like **stdio** or **SSE** (Note that most clients support stdio, but not SSE at the moment). The transport layer handles message framing, delivery, and error handling.

## Quick Start

### Creating a Server

MCP servers follow a decorator approach to register handlers for MCP primitives like resources, prompts, and tools. The goal is to provide a simple interface for exposing capabilities to LLM clients.

```
# /// script
# dependencies = [
#   "mcp"
# ]
# ///
from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
import mcp.server.stdio
import mcp.types as types

# Create a server instance
server = Server("example-server")

# Add prompt capabilities
@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    return [
        types.Prompt(
            name="example-prompt",
            description="An example prompt template",
            arguments=[
                types.PromptArgument(
                    name="arg1",
                    description="Example argument",
                    required=True
                )
            ]
        )
    ]

@server.get_prompt()
async def handle_get_prompt(
    name: str,
    arguments: dict[str, str] | None
) -> types.GetPromptResult:
    if name != "example-prompt":
        raise ValueError(f"Unknown prompt: {name}")

    return types.GetPromptResult(
        description="Example prompt",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text="Example prompt text"
                )
            )
        ]
    )

async def run():
    # Run the server as STDIO
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="example",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                )
            )
        )

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())
```

### Creating a Client

```
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# Create server parameters for stdio connection
server_params = StdioServerParameters(
    command="path/to/server",
    args=[], # Optional command line arguments
    env=None # Optional environment variables
)

async with stdio_client(server_params) as (read, write):
    async with ClientSession(read, write) as session:
        # Initialize the connection
        await session.initialize()

        # List available resources
        resources = await session.list_resources()

        # List available prompts
        prompts = await session.list_prompts()

        # List available tools
        tools = await session.list_tools()

        # Read a resource
        resource = await session.read_resource("file://some/path")

        # Call a tool
        result = await session.call_tool("tool-name", arguments={"arg1": "value"})

        # Get a prompt
        prompt = await session.get_prompt("prompt-name", arguments={"arg1": "value"})
```

## Primitives

The MCP Python SDK provides decorators that map to the core protocol primitives. Each primitive follows a different interaction pattern based on how it is controlled and used:

| Primitive | Control | Description | Example Use |
| --- | --- | --- | --- |
| Prompts | User-controlled | Interactive templates invoked by user choice | Slash commands, menu options |
| Resources | Application-controlled | Contextual data managed by the client application | File contents, API responses |
| Tools | Model-controlled | Functions exposed to the LLM to take actions | API calls, data updates |

### User-Controlled Primitives

**Prompts** are designed to be explicitly selected by users for their interactions with LLMs.

| Decorator | Description |
| --- | --- |
| `@server.list_prompts()` | List available prompt templates |
| `@server.get_prompt()` | Get a specific prompt with arguments |

### Application-Controlled Primitives

**Resources** are controlled by the client application, which decides how and when they should be used based on its own logic.

| Decorator | Description |
| --- | --- |
| `@server.list_resources()` | List available resources |
| `@server.read_resource()` | Read a specific resource's content |
| `@server.subscribe_resource()` | Subscribe to resource updates |

### Model-Controlled Primitives

**Tools** are exposed to LLMs to enable automated actions, with user approval.

| Decorator | Description |
| --- | --- |
| `@server.list_tools()` | List available tools |
| `@server.call_tool()` | Execute a tool with arguments |

### Server Management

Additional decorators for server functionality:

| Decorator | Description |
| --- | --- |
| `@server.set_logging_level()` | Update server logging level |

### Capabilities

MCP servers declare capabilities during initialization. These map to specific decorators:

| Capability | Feature Flag | Decorators | Description |
| --- | --- | --- | --- |
| `prompts` | `listChanged` | `@list_prompts`   `@get_prompt` | Prompt template management |
| `resources` | `subscribe`   `listChanged` | `@list_resources`   `@read_resource`   `@subscribe_resource` | Resource exposure and updates |
| `tools` | `listChanged` | `@list_tools`   `@call_tool` | Tool discovery and execution |
| `logging` | \- | `@set_logging_level` | Server logging configuration |
| `completion` | \- | `@complete_argument` | Argument completion suggestions |

Capabilities are negotiated during connection initialization. Servers only need to implement the decorators for capabilities they support.

## Client Interaction

The MCP Python SDK enables servers to interact with clients through request context and session management. This allows servers to perform operations like LLM sampling and progress tracking.

### Request Context

The Request Context provides access to the current request and client session. It can be accessed through `server.request_context` and enables:

- Sampling from the client's LLM
- Sending progress updates
- Logging messages
- Accessing request metadata

Example using request context for LLM sampling:

```
@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    # Access the current request context
    context = server.request_context

    # Use the session to sample from the client's LLM
    result = await context.session.create_message(
        messages=[
            types.SamplingMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text="Analyze this data: " + json.dumps(arguments)
                )
            )
        ],
        max_tokens=100
    )

    return [types.TextContent(type="text", text=result.content.text)]
```

Using request context for progress updates:

```
@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    context = server.request_context

    if progress_token := context.meta.progressToken:
        # Send progress notifications
        await context.session.send_progress_notification(
            progress_token=progress_token,
            progress=0.5,
            total=1.0
        )

    # Perform operation...

    if progress_token:
        await context.session.send_progress_notification(
            progress_token=progress_token,
            progress=1.0,
            total=1.0
        )

    return [types.TextContent(type="text", text="Operation complete")]
```

The request context is automatically set for each request and provides a safe way to access the current client session and request metadata.

## Documentation

- [Model Context Protocol documentation](https://modelcontextprotocol.io/)
- [Model Context Protocol specification](https://spec.modelcontextprotocol.io/)
- [Officially supported servers](https://github.com/modelcontextprotocol/servers)

## Contributing

We are passionate about supporting contributors of all levels of experience and would love to see you get involved in the project. See the [contributing guide](https://github.com/modelcontextprotocol/python-sdk/blob/main/CONTRIBUTING.md) to get started.

## License

This project is licensed under the MIT License - see the LICENSE file for details.