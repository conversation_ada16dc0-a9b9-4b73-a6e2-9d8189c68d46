---
title: "Sur<PERSON> vs MinerU: OCR Engine Comparison for Multilingual Document Processing"
created: 2025-07-01
tags:
  - AI/OCR
  - AI/DocumentProcessing
  - AI/Comparison
  - AI/LayoutAnalysis
description: Comprehensive comparison between Surya and MinerU OCR engines for English/Japanese/Chinese document processing with layout analysis
---
# Model List

## Model for dev
```
PaddleOCR v4 for multilingual text recognition, LayoutReader/DLAFormer for reading order detection, 
Coqui TTS XTTS-v2 for high-quality speech synthesis,

```
## Model for PROD
```
 Google Cloud Vision API (98% WER) or Azure Document Intelligence for OCR, paired with Microsoft's LayoutLMv3 for layout understanding,

```

## Model for cost/performance
```
PaddleOCR v4 emerges as the standout choice, offering excellent multilingual support across 80+ languages with lightweight models under 10MB. Tecmint When combined with LayoutParser (built on Detectron2) for layout analysis and the ReadingBank/LayoutReader framework for reading order detection, github this stack achieves performance approaching commercial solutions at zero licensing cost. 

The latest Surya toolkit shows particular promise as an integrated solution handling OCR, layout analysis, and reading order in a single framework

https://github.com/doc-analysis/ReadingBank
https://github.com/datalab-to/surya
https://www.tecmint.com/python-text-extraction-from-images/
https://github.com/JaidedAI/EasyOCR


 PaddleOCR v4 and PaddleStructure 
  LayoutLMv1 

 MinerU and llama-ocr
 OmniDocBench 
 MonkeyOCR 
 
 
 ```

## Model for realtime
```
 optimize with EasyOCR for GPU-accelerated text detection, lightweight LayoutParser models, and the compact Chatterbox TTS model (0.5B parameters). 
 
```
## OcrFlux *also have others OCR Model*
https://github.com/chatdoc-com/OCRFlux

---





# Surya vs MinerU: OCR Engine Comparison

## Project Requirements
- **OCR Engine**: English, Japanese, Chinese support
- **Layout Analysis**: Reading order detection, column detection, title recognition
- **TTS Engine**: Multilingual text-to-speech

## 🏆 Comparison Overview

### **Surya** ⭐ **RECOMMENDED**

**Strengths:**
- ✅ **Purpose-built for OCR**: Custom-trained models specifically for OCR and layout analysis
- ✅ **Superior OCR Quality**: Benchmarks favorably against cloud services (Google, AWS, Azure)
- ✅ **90+ Languages**: Excellent support for English, Japanese, Chinese with dedicated models
- ✅ **Integrated Layout Analysis**: Built-in reading order detection, column detection, title recognition
- ✅ **Modern Architecture**: Uses state-of-the-art transformer models (Segformer)
- ✅ **Active Development**: Regularly updated by VikParuchuri
- ✅ **Permissive License**: More business-friendly licensing

**Key Features:**
```markdown
Surya is a document OCR toolkit that does:
- OCR in 90+ languages that benchmarks favorably vs cloud services
- Line-level text detection in any language
- Layout analysis (table, image, header, etc detection)
- Reading order detection
```

**Usage Example:**
```python
from PIL import Image
from surya.ocr import run_ocr
from surya.model.detection import segformer
from surya.model.recognition.model import load_model
from surya.model.recognition.processor import load_processor

image = Image.open(IMAGE_PATH)
langs = ["en", "ja", "zh"] # English, Japanese, Chinese
det_processor, det_model = segformer.load_processor(), segformer.load_model()

# Layout Analysis
from surya.layout import batch_layout_detection
layout_predictions = batch_layout_detection([image], model, processor, line_predictions)

# Reading Order Detection
from surya.ordering import batch_ordering
order_predictions = batch_ordering([image], model, processor, layout_predictions)
```

### **MinerU**

**Strengths:**
- ✅ **Comprehensive Pipeline**: One-stop solution for PDF, webpage, e-book extraction
- ✅ **Established Backend**: Uses PaddleOCR (mature, proven technology)
- ✅ **Multiple Format Support**: Beyond just PDFs
- ✅ **Complete Ecosystem**: More integrated document processing features

**Weaknesses:**
- ❌ **Older OCR Technology**: Relies on PaddleOCR instead of cutting-edge models
- ❌ **License Concerns**: AGPL license - restrictive for commercial use
- ❌ **Less Specialized**: Jack-of-all-trades approach may compromise OCR quality

**Key Info:**
```markdown
The project currently leverages PyMuPDF to deliver advanced functionalities; 
however, its adherence to the AGPL license may impose limitations on certain use cases.

Acknowledgments:
- PaddleOCR
- PyMuPDF
- fast-langdetect
- pdfminer.six
```

## 🎯 Feature Comparison

| Feature | Surya | MinerU |
|---------|-------|---------|
| **OCR Quality** | ⭐⭐⭐⭐⭐ Custom models | ⭐⭐⭐ PaddleOCR |
| **Language Support** | 90+ languages | Multilingual via PaddleOCR |
| **Layout Analysis** | ⭐⭐⭐⭐⭐ Built-in | ⭐⭐⭐ Basic |
| **Reading Order** | ⭐⭐⭐⭐⭐ Dedicated model | ⭐⭐ Limited |
| **Performance** | ⭐⭐⭐⭐⭐ Optimized | ⭐⭐⭐ Good |
| **License** | ⭐⭐⭐⭐⭐ Permissive | ⭐⭐ AGPL |
| **Document Formats** | ⭐⭐⭐ PDF focus | ⭐⭐⭐⭐⭐ Multiple |
| **Maintenance** | ⭐⭐⭐⭐⭐ Active | ⭐⭐⭐⭐ Active |

## 🏁 Final Recommendation: **Surya**

**Why Surya is better for this project:**

1. **Higher OCR Accuracy** - Custom transformer models vs older PaddleOCR
2. **Perfect Feature Match** - Exactly matches requirements (OCR + Layout Analysis)
3. **Better Asian Language Support** - Likely superior Japanese/Chinese recognition
4. **Licensing** - No AGPL restrictions for commercial use
5. **Performance** - More efficient, modern architecture
6. **Specialization** - Purpose-built for OCR tasks

## 🔄 When to Consider MinerU

- If you need a complete document processing pipeline beyond OCR
- If you're already invested in the PaddleOCR ecosystem
- If you need to process multiple document formats beyond PDF
- If AGPL license is acceptable for your use case

## 💡 Implementation Strategy

**Recommended Approach:**
1. **Start with Surya** for core OCR + Layout Analysis needs
2. **Add complementary tools** as needed:
   - **Marker** for high-accuracy PDF to Markdown conversion (uses Surya)
   - **Docling** for advanced document processing with vision models
3. **Consider MinerU later** if additional document processing capabilities are needed

## 🛠️ Next Steps

1. Install and test Surya with sample documents
2. Evaluate OCR quality on your specific document types
3. Test layout analysis accuracy for your use cases
4. Integrate with chosen TTS solution (Qwen-TTS or Azure TTS)

## 📚 Related Tools in Codebase

- **Marker**: Uses Surya for OCR, high accuracy PDF parsing
- **Docling**: IBM's solution with vision model support
- **Qwen-TTS**: Recommended for multilingual TTS
- **Azure TTS**: Already configured backup TTS solution

---
# TTS

## MeloTTS
https://github.com/myshell-ai/MeloTTS
<span style="background:#ff4d4f">failed</span> , as only support eng/chinese mixture

## TTSMaker 
https://ttsmaker.com/
<span style="background:#ff4d4f">failed</span>
You have to access website and upload/download voice
## fishaudio
https://speech.fish.audio/zh/
[[🦜🦜🦜fishaudio fish-speech Brand new TTS solution]]
<span style="background:#ff4d4f">failed</span>
Gave up


## Kokoto
[[🦜🦜hexgrad Kokoro-82M · Hugging Face]]
<span style="background:#ff4d4f">failed</span>
cross-language not well

## F5-TTS
https://github.com/SWivid/F5-TTS

pip install f5-tts

---
## Retry fish audio
https://huggingface.co/fishaudio/openaudio-s1-mini

```
apt install portaudio19-dev libsox-dev ffmpeg
uv sync --python 3.10
myuv

uv add /databank/install/jetpack6/v61/offcial/torch-2.8.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchvision-0.23.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/torchaudio-2.8.0-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_python-4.11.0-py3-none-any.whl
uv add /databank/install/jetpack6/v61/offcial/opencv_contrib_python-*********-cp310-cp310-linux_aarch64.whl
uv add /databank/install/flash-attention/dist/flash_attn-2.6.3-cp310-cp310-linux_aarch64.whl
uv add /databank/install/jetpack6/v61/offcial/triton-3.4.0-cp310-cp310-linux_aarch64.whl 

uv run python -m tools.run_webui --device cuda 

uv run python fish_speech/models/dac/inference.py \
    -i "sample.wav" \
    --checkpoint-path "checkpoints/openaudio-s1-mini/codec.pth"
	

uv run python fish_speech/models/text2semantic/inference.py \
    --text "アジェンダ: 1. Minute of meeting (Mom) current development progress. 2. What will be done next on Mom Application3.  要約: 1. 会議の内容は主に、現在のMoMアプリケーションの開発進捗、次に何をするべきか、 そしてAzure環境へのアクセスについてのアップデートについての議論でした。会議では、VPCのReachability Analyzerの進行状況や、ECSのコンテナランタイムセキュリティについての議論が行われました。" \
    --prompt-text "2段組レイアウト左側のコンテンツ。右側のコンテンツ2段組レイアウトは一般的な方法で。通常右側のコラムは通常二次的なコンテンツ補足説明または関連図を配置するために使用されます。テストではシステムは見上げテストではこのレイアウトを使用してシステムが水平方向の読み上げ順序を正しく認識できるか検証できます。標準的な読み上げ順序はまず左側のコラムのすべてのコンテンツを読み次に右側のコラムのコンテンツを読むべきです。システムが読み上げ順序を正しく認識できるように明確な視覚的な区切り線異なる背景色または番号付きシステムをしレイアウトはシステムの空間認識能力に対してより高い要求をします。複雑なプレゼンテーションでは二段組レイアウトが他のレイアウト要素と混合して使用される場合がありこれはシステムの認識能力にとってより大きな課題となります。左側のコラムは通常主要なコンテンツや前提条件を配置するために使用され読み上げの開始位置です。設計時には左側のコンテンツと右側のコンテンツが明確に視覚的に区別されるようにする必要があります。システムがこの複雑な状況を正しく処理できるかどうかに注意を払う必要があります。" \
    --prompt-tokens "fake.npy" \
    --compile
    
uv run python fish_speech/models/dac/inference.py \
    -i "temp/codes_0.npy" \
	-o "meeting_output.wav"

-------------Web HTTP Server-------------------
uv run python -m tools.api_server \
    --listen 0.0.0.0:8080 \
    --llama-checkpoint-path "checkpoints/openaudio-s1-mini" \
    --decoder-checkpoint-path "checkpoints/openaudio-s1-mini/codec.pth" \
    --decoder-config-name modded_dac_vq \
    --compile


  Basic Usage

  # Simple text-to-speech without reference audio (random voice)
  uv run python tools/api_client.py --text "Hello, this is a test message"

  # With reference audio for voice cloning
  uv run python tools/api_client.py \
      --text "Hello, this is a test message" \
      --reference_audio "path/to/reference_audio.wav" \
      --reference_text "Reference text that matches the audio"

  # Multiple reference audios for better voice cloning
  uv run python tools/api_client.py \
      --text "Hello, this is a test message" \
      --reference_audio "audio1.wav" "audio2.wav" \
      --reference_text "Text for audio1" "Text for audio2"

  Key Parameters

  - --url (default: http://127.0.0.1:8080/v1/tts) - API server URL
  - --text (required) - Text to synthesize
  - --reference_audio - Audio file(s) for voice cloning
  - --reference_text - Text corresponding to reference audio
  - --output (default: generated_audio) - Output filename
  - --format - Output format: wav, mp3, flac (default: wav)
  - --play - Auto-play generated audio (default: true)

  Advanced Options

  # Custom generation parameters
  uv run python tools/api_client.py \
      --text "Your text here" \
      --temperature 0.7 \
      --top_p 0.9 \
      --max_new_tokens 2048 \
      --chunk_length 200 \
      --repetition_penalty 1.2 \
      --seed 42

  # Streaming mode
  uv run python tools/api_client.py \
      --text "Your text here" \
      --streaming true

  # Save without playing
  uv run python tools/api_client.py \
      --text "Your text here" \
      --output "my_speech" \
      --format "mp3" \
      --no-play

  Example with Emotion Markers

  uv run python tools/api_client.py \
      --text "(excited) Hello everyone! (laughing) This is amazing!"

uv run python tools/api_client.py --text  "アジェンダ: 1. Minute of meeting (Mom) current development progress. 2. What will be done next on Mom Application3.  要約: 1. 会議の内容は主に、現在のMoMアプリケーションの開発進捗、次に何をするべきか、 そしてAzure環境へのアクセスについてのアップデートについての議論でした。会議では、VPCのReachability Analyzerの進行状況や、ECSのコンテナランタイムセキュリティについての議論が行われました。"
	
```
---

# OCR 

## OCRFlux
https://github.com/chatdoc-com/OCRFlux.git

-- demo
https://pdfparser.io/pdfparser/#/view/336b81bd-eae7-49b4-bd05-c584f92db463?src=pdfparser
```
sudo apt-get update
sudo apt-get install poppler-utils poppler-data ttf-mscorefonts-installer msttcorefonts fonts-crosextra-caladea fonts-crosextra-carlito gsfonts lcdf-typetools


```